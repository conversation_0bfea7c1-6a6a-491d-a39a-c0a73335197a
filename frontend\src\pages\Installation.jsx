import { Link } from 'react-router-dom';

const Installation = () => {
  const installationTypes = [
    {
      id: 1,
      title: "Central Air Conditioning",
      description: "Complete central AC system installation for whole-home cooling comfort.",
      features: ["Energy-efficient units", "Professional ductwork", "Smart thermostat", "5-year warranty"],
      price: "Starting at $3,500",
      image: "❄️"
    },
    {
      id: 2,
      title: "Heating Systems",
      description: "Gas, electric, and heat pump installation for reliable home heating.",
      features: ["High-efficiency units", "Professional installation", "Safety inspections", "Extended warranty"],
      price: "Starting at $2,800",
      image: "🔥"
    },
    {
      id: 3,
      title: "Ductless Mini-Split",
      description: "Flexible ductless systems perfect for room additions and zoned comfort.",
      features: ["Zone control", "Energy efficient", "Quiet operation", "Easy installation"],
      price: "Starting at $1,200",
      image: "🏠"
    },
    {
      id: 4,
      title: "Heat Pumps",
      description: "Efficient heat pump systems for year-round heating and cooling.",
      features: ["Dual functionality", "Energy savings", "Eco-friendly", "Smart controls"],
      price: "Starting at $4,200",
      image: "♻️"
    }
  ];

  const installationProcess = [
    {
      step: 1,
      title: "Free Consultation",
      description: "We assess your home and discuss your comfort needs and budget.",
      icon: "📋"
    },
    {
      step: 2,
      title: "Custom Design",
      description: "Our experts design a system tailored to your home's specific requirements.",
      icon: "📐"
    },
    {
      step: 3,
      title: "Professional Installation",
      description: "Certified technicians install your system with precision and care.",
      icon: "🔧"
    },
    {
      step: 4,
      title: "Testing & Training",
      description: "We test the system thoroughly and train you on its operation.",
      icon: "✅"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-600 to-blue-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Professional HVAC Installation
              </h1>
              <p className="text-xl mb-8 text-green-100">
                Expert installation of heating, ventilation, and air conditioning systems.
                We ensure your new HVAC system operates efficiently and reliably for years to come.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/contact"
                  className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors duration-200 text-center"
                >
                  Get Free Estimate
                </Link>
                <a
                  href="tel:************"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors duration-200 text-center"
                >
                  Call (*************
                </a>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <h3 className="text-2xl font-bold mb-6">Why Choose Our Installation?</h3>
                <ul className="space-y-4">
                  <li className="flex items-center">
                    <svg className="h-6 w-6 text-green-300 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Licensed & Insured Technicians
                  </li>
                  <li className="flex items-center">
                    <svg className="h-6 w-6 text-green-300 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Energy-Efficient Systems
                  </li>
                  <li className="flex items-center">
                    <svg className="h-6 w-6 text-green-300 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Comprehensive Warranties
                  </li>
                  <li className="flex items-center">
                    <svg className="h-6 w-6 text-green-300 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Free Maintenance Check
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Installation Types */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              HVAC Installation Services
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We install all types of HVAC systems with professional expertise and attention to detail.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {installationTypes.map((type) => (
              <div key={type.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-200">
                <div className="text-5xl mb-4 text-center">{type.image}</div>
                <h3 className="text-xl font-semibold mb-3 text-center">{type.title}</h3>
                <p className="text-gray-600 mb-4 text-center">{type.description}</p>
                <ul className="space-y-2 mb-6">
                  {type.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <svg className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600 mb-3">{type.price}</div>
                  <Link
                    to="/contact"
                    className="w-full bg-green-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 block text-center"
                  >
                    Get Quote
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Installation Process */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Installation Process
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We follow a proven process to ensure your HVAC installation is done right the first time.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {installationProcess.map((process) => (
              <div key={process.step} className="text-center">
                <div className="bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-3xl">{process.icon}</span>
                </div>
                <div className="bg-green-600 text-white w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-4 text-sm font-bold">
                  {process.step}
                </div>
                <h3 className="text-xl font-semibold mb-3">{process.title}</h3>
                <p className="text-gray-600">{process.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Benefits of Professional Installation
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Professional installation ensures optimal performance, efficiency, and longevity of your HVAC system.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">⚡</div>
              <h3 className="text-xl font-semibold mb-3">Energy Efficiency</h3>
              <p className="text-gray-600">
                Proper installation maximizes energy efficiency, reducing your utility bills and environmental impact.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">🛡️</div>
              <h3 className="text-xl font-semibold mb-3">Warranty Protection</h3>
              <p className="text-gray-600">
                Professional installation protects your manufacturer's warranty and includes our workmanship guarantee.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">🔧</div>
              <h3 className="text-xl font-semibold mb-3">Proper Sizing</h3>
              <p className="text-gray-600">
                We ensure your system is properly sized for your space, preventing short cycling and inefficiency.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">🏠</div>
              <h3 className="text-xl font-semibold mb-3">Code Compliance</h3>
              <p className="text-gray-600">
                All installations meet local building codes and safety standards for your peace of mind.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">⏰</div>
              <h3 className="text-xl font-semibold mb-3">Longevity</h3>
              <p className="text-gray-600">
                Professional installation extends the life of your HVAC system, protecting your investment.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">🎯</div>
              <h3 className="text-xl font-semibold mb-3">Optimal Performance</h3>
              <p className="text-gray-600">
                Expert installation ensures your system operates at peak performance from day one.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-green-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready for Professional Installation?
          </h2>
          <p className="text-xl mb-8 text-green-100 max-w-3xl mx-auto">
            Get a free, no-obligation estimate for your HVAC installation. Our experts will help you choose the perfect system for your needs and budget.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors duration-200"
            >
              Get Free Estimate
            </Link>
            <a
              href="tel:************"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors duration-200"
            >
              Call (*************
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Installation;
import { Link } from 'react-router-dom';

const EnergyManagement = () => {
  const energySolutions = [
    {
      id: 1,
      title: "Smart Thermostats",
      description: "Intelligent temperature control that learns your schedule and preferences.",
      features: ["WiFi connectivity", "Mobile app control", "Energy usage reports", "Automatic scheduling"],
      savings: "Up to 23% on energy bills",
      icon: "🌡️"
    },
    {
      id: 2,
      title: "Energy Audits",
      description: "Comprehensive assessment of your home's energy efficiency and recommendations.",
      features: ["Thermal imaging", "Duct leakage testing", "Insulation assessment", "Detailed report"],
      savings: "Identify 15-30% savings",
      icon: "🔍"
    },
    {
      id: 3,
      title: "Zoning Systems",
      description: "Control temperature in different areas of your home independently.",
      features: ["Zone dampers", "Multiple thermostats", "Customized comfort", "Reduced energy waste"],
      savings: "Up to 30% on heating/cooling",
      icon: "🏠"
    },
    {
      id: 4,
      title: "High-Efficiency Equipment",
      description: "Upgrade to ENERGY STAR certified HVAC equipment for maximum efficiency.",
      features: ["ENERGY STAR certified", "Variable speed technology", "Advanced controls", "Rebate eligible"],
      savings: "20-40% energy reduction",
      icon: "⭐"
    }
  ];

  const energyTips = [
    {
      tip: "Change air filters regularly",
      description: "Replace filters every 1-3 months to maintain airflow and efficiency.",
      impact: "5-15% energy savings"
    },
    {
      tip: "Seal air leaks",
      description: "Seal gaps around windows, doors, and ductwork to prevent energy loss.",
      impact: "10-20% energy savings"
    },
    {
      tip: "Use programmable thermostats",
      description: "Set temperatures back when away or sleeping to reduce energy usage.",
      impact: "10-23% energy savings"
    },
    {
      tip: "Maintain your HVAC system",
      description: "Regular maintenance keeps your system running efficiently.",
      impact: "5-40% energy savings"
    },
    {
      tip: "Improve insulation",
      description: "Proper insulation reduces heating and cooling loads.",
      impact: "15-30% energy savings"
    },
    {
      tip: "Upgrade to efficient equipment",
      description: "Modern HVAC systems are significantly more efficient than older models.",
      impact: "20-40% energy savings"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-500 to-emerald-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Smart Energy Management Solutions
              </h1>
              <p className="text-xl mb-8 text-green-100">
                Reduce your energy costs and environmental impact with our advanced HVAC energy management solutions.
                Save money while staying comfortable year-round.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/contact"
                  className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors duration-200 text-center"
                >
                  Get Energy Audit
                </Link>
                <a
                  href="tel:************"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors duration-200 text-center"
                >
                  Call (*************
                </a>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <h3 className="text-2xl font-bold mb-6">Energy Savings Potential</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Smart Thermostats</span>
                    <span className="font-bold text-green-300">23%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Zoning Systems</span>
                    <span className="font-bold text-green-300">30%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Efficient Equipment</span>
                    <span className="font-bold text-green-300">40%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Energy Audits</span>
                    <span className="font-bold text-green-300">25%</span>
                  </div>
                  <div className="border-t border-white/20 pt-4 mt-4">
                    <div className="flex justify-between items-center text-lg font-bold">
                      <span>Total Potential Savings</span>
                      <span className="text-green-300">Up to 50%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Energy Solutions */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Energy Management Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover how our energy management solutions can reduce your utility bills and improve comfort.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {energySolutions.map((solution) => (
              <div key={solution.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-200">
                <div className="text-5xl mb-4 text-center">{solution.icon}</div>
                <h3 className="text-xl font-semibold mb-3 text-center">{solution.title}</h3>
                <p className="text-gray-600 mb-4 text-center">{solution.description}</p>
                <ul className="space-y-2 mb-6">
                  {solution.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <svg className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600 mb-3">{solution.savings}</div>
                  <Link
                    to="/contact"
                    className="w-full bg-green-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 block text-center"
                  >
                    Learn More
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Energy Tips */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Energy Saving Tips
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Simple steps you can take today to start saving energy and reducing your utility bills.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {energyTips.map((tip, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
                <div className="flex items-start">
                  <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">{tip.tip}</h3>
                    <p className="text-gray-600 mb-3">{tip.description}</p>
                    <div className="text-sm font-medium text-green-600">{tip.impact}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* ROI Calculator */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 md:p-12 text-white">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-4">
                  Calculate Your Potential Savings
                </h2>
                <p className="text-xl text-green-100 mb-6">
                  See how much you could save with our energy management solutions.
                  Most customers see a return on investment within 2-3 years.
                </p>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center">
                    <svg className="h-5 w-5 text-green-300 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Average 25% reduction in energy bills
                  </li>
                  <li className="flex items-center">
                    <svg className="h-5 w-5 text-green-300 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Increased home value
                  </li>
                  <li className="flex items-center">
                    <svg className="h-5 w-5 text-green-300 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Improved comfort and air quality
                  </li>
                  <li className="flex items-center">
                    <svg className="h-5 w-5 text-green-300 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Reduced environmental impact
                  </li>
                </ul>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                <h3 className="text-xl font-bold mb-4">Example Savings</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Current monthly bill:</span>
                    <span className="font-bold">$200</span>
                  </div>
                  <div className="flex justify-between">
                    <span>With energy management:</span>
                    <span className="font-bold text-green-300">$150</span>
                  </div>
                  <div className="border-t border-white/20 pt-4">
                    <div className="flex justify-between text-lg font-bold">
                      <span>Monthly savings:</span>
                      <span className="text-green-300">$50</span>
                    </div>
                    <div className="flex justify-between text-lg font-bold">
                      <span>Annual savings:</span>
                      <span className="text-green-300">$600</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-green-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Start Saving Energy Today
          </h2>
          <p className="text-xl mb-8 text-green-100 max-w-3xl mx-auto">
            Contact us for a free energy audit and discover how much you can save with our energy management solutions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors duration-200"
            >
              Schedule Energy Audit
            </Link>
            <a
              href="tel:************"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors duration-200"
            >
              Call (*************
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default EnergyManagement;
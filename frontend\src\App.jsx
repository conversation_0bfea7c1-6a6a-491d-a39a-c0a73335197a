// src/App.jsx
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import MainLayout from "./layouts/MainLayout";
import Contact from "./pages/Contact";




function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<MainLayout />}>
            <Route path="/contact" element={<Contact />} />
        </Route>
        {/* Example page without MainLayout (e.g. login) */}
        {/* <Route path="/login" element={<Login />} /> */}

        {/* 404 fallback */}
       
      </Routes>
    </Router>
  );
}

export default App;

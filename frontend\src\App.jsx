// src/App.jsx
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import MainLayout from "./layouts/MainLayout";
import Home from "./pages/Home";
import Contact from "./pages/Contact";
import Services from "./pages/Services";
import Installation from "./pages/Installation";
import EnergyManagement from "./pages/EnergyManagement";
import Contracts from "./pages/Contracts";

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<MainLayout />}>
          <Route index element={<Home />} />
          <Route path="services" element={<Services />} />
          <Route path="installation" element={<Installation />} />
          <Route path="energy-management" element={<EnergyManagement />} />
          <Route path="contracts" element={<Contracts />} />
          <Route path="contact" element={<Contact />} />
        </Route>
        {/* Example page without MainLayout (e.g. login) */}
        {/* <Route path="/login" element={<Login />} /> */}

        {/* 404 fallback */}
        <Route path="*" element={<div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">404 - Page Not Found</h1>
            <p className="text-gray-600">The page you're looking for doesn't exist.</p>
          </div>
        </div>} />
      </Routes>
    </Router>
  );
}

export default App;

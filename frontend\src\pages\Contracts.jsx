import { Link } from 'react-router-dom';

const Contracts = () => {
  const contractTypes = [
    {
      id: 1,
      title: "Residential Maintenance",
      description: "Comprehensive maintenance plans for homeowners to keep HVAC systems running efficiently.",
      features: [
        "Bi-annual system inspections",
        "Filter replacements included",
        "Priority service scheduling",
        "15% discount on repairs",
        "Extended equipment warranties",
        "Energy efficiency reports"
      ],
      price: "Starting at $199/year",
      popular: false,
      icon: "🏠"
    },
    {
      id: 2,
      title: "Commercial Maintenance",
      description: "Tailored maintenance contracts for businesses to ensure optimal HVAC performance.",
      features: [
        "Quarterly system inspections",
        "Preventive maintenance",
        "24/7 emergency service",
        "Detailed maintenance reports",
        "Budget planning assistance",
        "Compliance documentation"
      ],
      price: "Custom pricing",
      popular: true,
      icon: "🏢"
    },
    {
      id: 3,
      title: "Emergency Service",
      description: "Round-the-clock emergency service contracts for critical HVAC system failures.",
      features: [
        "24/7 emergency response",
        "2-hour response guarantee",
        "No overtime charges",
        "Priority parts availability",
        "Emergency diagnostic service",
        "Temporary equipment if needed"
      ],
      price: "Starting at $299/year",
      popular: false,
      icon: "🚨"
    }
  ];

  const benefits = [
    {
      title: "Cost Savings",
      description: "Regular maintenance prevents costly breakdowns and extends equipment life.",
      icon: "💰"
    },
    {
      title: "Priority Service",
      description: "Contract customers receive priority scheduling for all service calls.",
      icon: "⚡"
    },
    {
      title: "Peace of Mind",
      description: "Know that your HVAC system is professionally maintained year-round.",
      icon: "🛡️"
    },
    {
      title: "Energy Efficiency",
      description: "Well-maintained systems operate more efficiently, reducing energy costs.",
      icon: "🌱"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-600 to-blue-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            HVAC Service Contracts
          </h1>
          <p className="text-xl mb-8 text-purple-100 max-w-3xl mx-auto">
            Protect your investment with our comprehensive HVAC service contracts.
            Ensure optimal performance, prevent costly breakdowns, and enjoy peace of mind year-round.
          </p>
          <Link
            to="/contact"
            className="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors duration-200"
          >
            Get Contract Quote
          </Link>
        </div>
      </section>

      {/* Contract Types */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Service Contract Options
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose the service contract that best fits your needs and budget.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {contractTypes.map((contract) => (
              <div key={contract.id} className={`relative bg-white border-2 rounded-lg p-8 hover:shadow-lg transition-shadow duration-200 ${
                contract.popular ? 'border-purple-500 shadow-lg' : 'border-gray-200'
              }`}>
                {contract.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-purple-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-6">
                  <div className="text-5xl mb-4">{contract.icon}</div>
                  <h3 className="text-2xl font-bold mb-2">{contract.title}</h3>
                  <p className="text-gray-600 mb-4">{contract.description}</p>
                  <div className="text-2xl font-bold text-purple-600">{contract.price}</div>
                </div>

                <ul className="space-y-3 mb-8">
                  {contract.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-600">
                      <svg className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>

                <Link
                  to="/contact"
                  className={`w-full py-3 px-6 rounded-lg font-semibold text-center block transition-colors duration-200 ${
                    contract.popular
                      ? 'bg-purple-600 text-white hover:bg-purple-700'
                      : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                  }`}
                >
                  Get Started
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose a Service Contract?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Service contracts provide numerous benefits that save you money and ensure reliable comfort.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="text-5xl mb-4">{benefit.icon}</div>
                <h3 className="text-xl font-semibold mb-3">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* What's Included */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What's Included in Every Contract
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              All our service contracts include these essential services to keep your HVAC system running smoothly.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold mb-3">System Inspections</h3>
              <p className="text-gray-600">
                Thorough inspections of all HVAC components to identify potential issues before they become problems.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">🧹</div>
              <h3 className="text-xl font-semibold mb-3">Cleaning & Maintenance</h3>
              <p className="text-gray-600">
                Professional cleaning of coils, filters, and other components to maintain optimal efficiency.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">⚙️</div>
              <h3 className="text-xl font-semibold mb-3">System Tune-ups</h3>
              <p className="text-gray-600">
                Calibration and adjustment of system components for peak performance and efficiency.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">📊</div>
              <h3 className="text-xl font-semibold mb-3">Performance Reports</h3>
              <p className="text-gray-600">
                Detailed reports on system performance, energy usage, and recommendations for improvements.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">🛠️</div>
              <h3 className="text-xl font-semibold mb-3">Minor Repairs</h3>
              <p className="text-gray-600">
                Small repairs and adjustments included in the contract to prevent larger issues.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
              <div className="text-4xl mb-4">📞</div>
              <h3 className="text-xl font-semibold mb-3">Priority Support</h3>
              <p className="text-gray-600">
                Priority scheduling for service calls and dedicated customer support for contract holders.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600">
              Common questions about our HVAC service contracts.
            </p>
          </div>

          <div className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-2">How often will my system be serviced?</h3>
              <p className="text-gray-600">
                Residential systems are typically serviced twice per year (spring and fall), while commercial systems
                may require quarterly service depending on usage and contract type.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-2">What if my system breaks down?</h3>
              <p className="text-gray-600">
                Contract customers receive priority service and discounted repair rates. Emergency contracts include
                24/7 response with guaranteed service times.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-2">Can I cancel my contract?</h3>
              <p className="text-gray-600">
                Yes, contracts can typically be cancelled with 30 days notice. However, we're confident you'll see
                the value in continued service and maintenance.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-2">Are parts and labor included?</h3>
              <p className="text-gray-600">
                Basic maintenance and minor repairs are included. Major repairs and parts are discounted for
                contract customers, with transparent pricing provided upfront.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-purple-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Protect Your HVAC Investment?
          </h2>
          <p className="text-xl mb-8 text-purple-100 max-w-3xl mx-auto">
            Join thousands of satisfied customers who trust HAVAC Solutions for their HVAC maintenance needs.
            Get a custom quote for your service contract today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors duration-200"
            >
              Get Contract Quote
            </Link>
            <a
              href="tel:************"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors duration-200"
            >
              Call (*************
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contracts;
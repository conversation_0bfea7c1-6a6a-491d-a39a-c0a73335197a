import React from "react";
import MainLayout from "../layouts/MainLayout";


export default function Contact() {
  return (
    <MainLayout>
    <section className="bg-gray-50 py-16">
      <div className="max-w-6xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800">Contact Us</h1>
          <p className="mt-2 text-gray-600">
            Have questions or need a quote? Get in touch with our team.
          </p>
        </div>

        {/* Grid Layout */}
        <div className="grid md:grid-cols-2 gap-12">
          {/* Contact Info */}
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-700">Office</h2>
              <p className="text-gray-500 mt-1">
                123 HVAC Street, Kolkata, India
              </p>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-gray-700">Phone</h2>
              <p className="text-gray-500 mt-1">+91 98765 43210</p>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-gray-700">Email</h2>
              <p className="text-gray-500 mt-1"><EMAIL></p>
            </div>
          </div>

          {/* Contact Form */}
          <form className="bg-white p-8 rounded-lg shadow-md space-y-4">
            <div>
              <label className="block text-gray-700 font-medium mb-1">
                Name
              </label>
              <input
                type="text"
                placeholder="Your Name"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-1">
                Email
              </label>
              <input
                type="email"
                placeholder="<EMAIL>"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-1">
                Message
              </label>
              <textarea
                placeholder="Your message..."
                rows={5}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none"
              ></textarea>
            </div>

            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 text-white font-semibold rounded-md hover:bg-blue-700 transition"
            >
              Send Message
            </button>
          </form>
        </div>
      </div>
    </section>
    </MainLayout>
  );
}
